import { clipboard, nativeImage } from 'electron'
import fs from 'node:fs'
import path from 'node:path'

/**
 * 将文件复制到系统剪切板
 * 支持 macOS、Windows、Linux 平台
 *
 * @param {string} filePath - 要复制的文件路径
 * @returns {Promise<boolean>} - 操作是否成功
 */
export async function copyFileToClipboard(filePath) {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      throw new Error(`File does not exist: ${filePath}`)
    }

    // 获取文件扩展名，确保是文件文件
    const ext = path.extname(filePath).toLowerCase()

    if (!ext) {
      throw new Error('Unsupported file format')
    }

    // 读取文件内容
    const imageBuffer = fs.readFileSync(filePath)

    // 创建 nativeImage 对象
    const image = nativeImage.createFromBuffer(imageBuffer)

    if (image.isEmpty()) {
      throw new Error('Failed to create image from file buffer')
    }

    // 根据平台进行不同的处理
    const platform = process.platform

    switch (platform) {
      case 'darwin': // macOS
        clipboard.writeImage(image)
        break

      case 'win32': // Windows
        clipboard.writeImage(image)
        break

      case 'linux': // Linux
        // Linux 平台的剪切板支持可能有限，但我们仍然尝试
        clipboard.writeImage(image)
        break

      default:
        console.warn(`Platform ${platform} may not be fully supported for clipboard operations`)
        clipboard.writeImage(image)
        break
    }

    console.log(`Successfully copied image to clipboard: ${filePath}`)
    return true
  }
  catch (error) {
    console.error('Failed to copy file to clipboard:', error.message)
    return false
  }
}
